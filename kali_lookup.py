#!/usr/bin/env python3
"""
Kali Linux Optimized Xbox Gamertag Lookup Tool
Designed for long-running background processes with robust error handling.
"""

import requests
import time
import json
import sys
import os
import signal
import logging
from typing import Dict, List, Optional
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('gamertag_lookup.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class GamertagResult:
    """Data class to store gamertag lookup results"""
    gamertag: str
    xuid: Optional[str] = None
    exists: bool = False
    has_minecraft_java: bool = False
    last_seen: Optional[str] = None
    error: Optional[str] = None

class KaliXboxAPIClient:
    """Robust Xbox API client optimized for Kali Linux"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://xbl.io/api/v2"
        self.session = requests.Session()
        self.session.headers.update({
            'X-Authorization': api_key,
            'Content-Type': 'application/json',
            'User-Agent': 'Xbox-Gamertag-Lookup/1.0'
        })
        
        # Conservative rate limiting for stability
        self.last_request_time = 0
        self.min_request_interval = 3.0  # 3 seconds between requests
        self.request_count_15s = 0
        self.request_count_300s = 0
        self.window_15s_start = 0
        self.window_300s_start = 0
        
        # Error tracking
        self.consecutive_errors = 0
        self.max_consecutive_errors = 5
        
    def _rate_limit(self):
        """Conservative rate limiting with error backoff"""
        current_time = time.time()
        
        # Reset windows
        if current_time - self.window_15s_start >= 15:
            self.request_count_15s = 0
            self.window_15s_start = current_time
            
        if current_time - self.window_300s_start >= 300:
            self.request_count_300s = 0
            self.window_300s_start = current_time
        
        # Check 15-second window (very conservative)
        if self.request_count_15s >= 4:
            wait_time = 15 - (current_time - self.window_15s_start)
            if wait_time > 0:
                logger.info(f"15s rate limit: waiting {wait_time:.1f}s...")
                time.sleep(wait_time + 1)
                self.request_count_15s = 0
                self.window_15s_start = time.time()
        
        # Check 5-minute window
        if self.request_count_300s >= 18:
            wait_time = 300 - (current_time - self.window_300s_start)
            if wait_time > 0:
                logger.info(f"5min rate limit: waiting {wait_time:.1f}s...")
                time.sleep(wait_time + 1)
                self.request_count_300s = 0
                self.window_300s_start = time.time()
        
        # Error backoff - increase delay if we've had consecutive errors
        if self.consecutive_errors > 0:
            backoff_delay = min(self.consecutive_errors * 2, 30)  # Max 30 seconds
            logger.info(f"Error backoff: waiting {backoff_delay}s after {self.consecutive_errors} consecutive errors")
            time.sleep(backoff_delay)
        
        # Basic interval
        time_since_last = current_time - self.last_request_time
        if time_since_last < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last
            time.sleep(sleep_time)
            
        # Update counters
        self.last_request_time = time.time()
        self.request_count_15s += 1
        self.request_count_300s += 1
    
    def _handle_response(self, response, gamertag: str):
        """Handle API response with error tracking"""
        if response.status_code == 200:
            self.consecutive_errors = 0  # Reset error counter on success
            return response.json()
        elif response.status_code == 404:
            self.consecutive_errors = 0
            return None
        elif response.status_code in [429, 400]:  # Rate limit errors
            self.consecutive_errors += 1
            logger.warning(f"Rate limit error for {gamertag}: {response.status_code}")
            if self.consecutive_errors >= self.max_consecutive_errors:
                logger.error(f"Too many consecutive errors ({self.consecutive_errors}). Sleeping for 5 minutes...")
                time.sleep(300)  # 5 minute break
                self.consecutive_errors = 0
            return None
        else:
            self.consecutive_errors += 1
            logger.error(f"API Error for {gamertag}: {response.status_code} - {response.text}")
            return None
    
    def search_gamertag(self, gamertag: str) -> Optional[Dict]:
        """Search for a gamertag with robust error handling"""
        self._rate_limit()
        
        try:
            url = f"{self.base_url}/search/{gamertag}"
            response = self.session.get(url, timeout=30)
            
            data = self._handle_response(response, gamertag)
            if data and 'people' in data and len(data['people']) > 0:
                for person in data['people']:
                    if person.get('gamertag', '').lower() == gamertag.lower():
                        return person
            return None
                
        except Exception as e:
            self.consecutive_errors += 1
            logger.error(f"Exception searching for {gamertag}: {e}")
            return None
    
    def get_title_history(self, xuid: str) -> Optional[List[Dict]]:
        """Get title history with error handling"""
        self._rate_limit()
        
        try:
            url = f"{self.base_url}/player/titleHistory/{xuid}"
            response = self.session.get(url, timeout=30)
            
            data = self._handle_response(response, f"XUID:{xuid}")
            if data:
                return data.get('titles', [])
            return None
                
        except Exception as e:
            self.consecutive_errors += 1
            logger.error(f"Exception getting title history for XUID {xuid}: {e}")
            return None

class MinecraftDetector:
    """Minecraft detection logic"""
    
    MINECRAFT_IDENTIFIERS = [
        'minecraft',
        'minecraft: java edition',
        'minecraft java edition',
        'minecraft: java & bedrock edition',
        'minecraft java & bedrock edition'
    ]
    
    @staticmethod
    def has_minecraft_java(titles: List[Dict]) -> bool:
        if not titles:
            return False
            
        for title in titles:
            title_name = title.get('name', '').lower()
            for identifier in MinecraftDetector.MINECRAFT_IDENTIFIERS:
                if identifier in title_name:
                    return True
        return False

class KaliGamertagLookupTool:
    """Main lookup tool optimized for Kali Linux"""
    
    def __init__(self, api_key: str):
        self.api_client = KaliXboxAPIClient(api_key)
        self.detector = MinecraftDetector()
        self.results: List[GamertagResult] = []
        self.minecraft_owners: List[GamertagResult] = []
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        self.shutdown_requested = False
        
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully"""
        logger.info(f"Received signal {signum}. Initiating graceful shutdown...")
        self.shutdown_requested = True
        
    def lookup_gamertag(self, gamertag: str) -> GamertagResult:
        """Lookup a single gamertag"""
        result = GamertagResult(gamertag=gamertag)
        
        logger.info(f"Checking {gamertag}...")
        
        profile = self.api_client.search_gamertag(gamertag)
        
        if not profile:
            logger.info(f"{gamertag}: Not found")
            return result
            
        result.exists = True
        result.xuid = profile.get('xuid')
        result.last_seen = profile.get('lastSeenDateTimeUtc')
        
        if result.xuid:
            titles = self.api_client.get_title_history(result.xuid)
            if titles is not None:
                result.has_minecraft_java = self.detector.has_minecraft_java(titles)
                
        if result.has_minecraft_java:
            logger.info(f"{gamertag}: ✓ HAS MINECRAFT JAVA EDITION")
            self.minecraft_owners.append(result)
        else:
            logger.info(f"{gamertag}: No Minecraft Java Edition")
            
        return result
    
    def save_progress(self, current_num: int):
        """Save current progress"""
        if self.minecraft_owners:
            data = [
                {
                    'gamertag': result.gamertag,
                    'xuid': result.xuid,
                    'last_seen': result.last_seen,
                    'has_minecraft_java': result.has_minecraft_java
                }
                for result in self.minecraft_owners
            ]
            
            filename = f"kali_minecraft_owners_{current_num:04d}.json"
            with open(filename, 'w') as f:
                json.dump(data, f, indent=2)
            
            logger.info(f"Progress saved to {filename}")
    
    def batch_lookup(self, start_num: int, end_num: int) -> List[GamertagResult]:
        """Batch lookup with progress saving"""
        logger.info(f"Starting Kali lookup: Blue{start_num:04d} to Blue{end_num:04d}")
        logger.info(f"Total: {end_num - start_num + 1} gamertags")
        logger.info(f"Conservative rate: ~3s per gamertag for stability")
        
        for i in range(start_num, end_num + 1):
            if self.shutdown_requested:
                logger.info("Shutdown requested. Saving progress and exiting...")
                break
                
            gamertag = f"Blue{i:04d}"
            result = self.lookup_gamertag(gamertag)
            self.results.append(result)
            
            # Save progress every 25 gamertags
            if (i - start_num + 1) % 25 == 0:
                progress = i - start_num + 1
                total = end_num - start_num + 1
                found = len(self.minecraft_owners)
                logger.info(f"Progress: {progress}/{total} ({progress/total*100:.1f}%) - Found: {found} Minecraft owners")
                self.save_progress(i)
                
        return self.minecraft_owners

def find_last_checked():
    """Find where to resume from"""
    # Based on user's output, we know we left off at Blue0158
    return 159  # Start from Blue0159

def load_config():
    """Load configuration"""
    config_file = Path("config.json")
    if config_file.exists():
        try:
            with open(config_file, 'r') as f:
                return json.load(f)
        except:
            pass
    return {}

def main():
    logger.info("Kali Linux Xbox Gamertag Lookup Tool Starting...")
    logger.info("=" * 50)
    
    # Load config
    config = load_config()
    api_key = config.get('api_key')
    
    if not api_key:
        api_key = input("Enter your OpenXBL API key: ").strip()
        if not api_key:
            logger.error("No API key provided. Exiting.")
            sys.exit(1)
    
    # Find starting point
    start_num = find_last_checked()
    logger.info(f"Resuming from: Blue{start_num:04d}")
    
    # Default to checking remaining gamertags
    end_num = 9999
    
    if len(sys.argv) > 1:
        try:
            end_num = int(sys.argv[1])
        except ValueError:
            logger.error("Invalid end number provided")
            sys.exit(1)
    
    logger.info(f"Will check Blue{start_num:04d} to Blue{end_num:04d}")
    logger.info(f"Total gamertags: {end_num - start_num + 1}")
    
    # Create tool and run
    tool = KaliGamertagLookupTool(api_key)
    
    try:
        minecraft_owners = tool.batch_lookup(start_num, end_num)
        
        logger.info("=" * 50)
        logger.info("KALI LOOKUP COMPLETED!")
        logger.info(f"Total Minecraft Java Edition owners found: {len(minecraft_owners)}")
        
        # Final save
        if minecraft_owners:
            final_file = f"kali_final_results_{start_num:04d}_{end_num:04d}.json"
            data = [
                {
                    'gamertag': result.gamertag,
                    'xuid': result.xuid,
                    'last_seen': result.last_seen,
                    'has_minecraft_java': result.has_minecraft_java
                }
                for result in minecraft_owners
            ]
            
            with open(final_file, 'w') as f:
                json.dump(data, f, indent=2)
            
            logger.info(f"Final results saved to: {final_file}")
        
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        tool.save_progress(start_num)

if __name__ == "__main__":
    main()
