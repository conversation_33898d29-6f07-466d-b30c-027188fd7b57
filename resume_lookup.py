#!/usr/bin/env python3
"""
Resume Xbox Gamertag Lookup Tool
This script can resume from where a previous lookup left off.
"""

import json
import os
import sys
from pathlib import Path

def find_last_checked_gamertag(results_file):
    """Find the last gamertag that was successfully checked"""
    if not os.path.exists(results_file):
        print(f"Results file {results_file} not found.")
        return None
    
    try:
        with open(results_file, 'r') as f:
            data = json.load(f)
        
        if not data:
            return None
            
        # Find the highest numbered gamertag
        max_num = -1
        for entry in data:
            gamertag = entry.get('gamertag', '')
            if gamertag.startswith('Blue'):
                try:
                    num = int(gamertag[4:])  # Extract number after "Blue"
                    max_num = max(max_num, num)
                except ValueError:
                    continue
        
        return max_num if max_num >= 0 else None
        
    except Exception as e:
        print(f"Error reading results file: {e}")
        return None

def get_api_key():
    """Get API key from config file or user input"""
    config_file = Path("config.json")
    
    # Try to load from config file first
    if config_file.exists():
        try:
            with open(config_file, 'r') as f:
                config = json.load(f)
                if 'api_key' in config and config['api_key']:
                    return config['api_key']
        except:
            pass
    
    # Get from user input
    api_key = input("Enter your OpenXBL API key: ").strip()
    if not api_key:
        print("No API key provided. Exiting.")
        sys.exit(1)
    
    return api_key

def main():
    print("Xbox Gamertag Lookup - Resume Tool")
    print("=" * 40)
    
    # Find existing results file
    results_files = [
        "partial_minecraft_java_owners_0000_9999.json",
        "minecraft_java_owners_0000_9999.json",
        "minecraft_java_owners.json"
    ]
    
    results_file = None
    for file in results_files:
        if os.path.exists(file):
            results_file = file
            break
    
    if not results_file:
        print("No existing results file found.")
        print("Available files:", [f for f in results_files if os.path.exists(f)])
        results_file = input("Enter results file name (or press Enter to start from Blue0000): ").strip()
        if not results_file:
            start_num = 0
        else:
            start_num = find_last_checked_gamertag(results_file)
    else:
        print(f"Found results file: {results_file}")
        start_num = find_last_checked_gamertag(results_file)
    
    if start_num is None:
        start_num = 0
        print("Starting from Blue0000")
    else:
        start_num += 1  # Start from next gamertag
        print(f"Resuming from Blue{start_num:04d}")
    
    # Get end number
    print(f"\nCurrent progress: Blue{start_num:04d}")
    print("Choose ending point:")
    print("1. Small batch (+100 gamertags)")
    print("2. Medium batch (+500 gamertags)")
    print("3. Large batch (+1000 gamertags)")
    print("4. Complete to Blue9999")
    print("5. Custom ending point")
    
    choice = input("Enter choice (1-5): ").strip()
    
    if choice == "1":
        end_num = min(start_num + 99, 9999)
    elif choice == "2":
        end_num = min(start_num + 499, 9999)
    elif choice == "3":
        end_num = min(start_num + 999, 9999)
    elif choice == "4":
        end_num = 9999
    elif choice == "5":
        try:
            end_num = int(input(f"Enter ending number ({start_num}-9999): "))
            if end_num < start_num or end_num > 9999:
                print("Invalid range.")
                sys.exit(1)
        except ValueError:
            print("Invalid number.")
            sys.exit(1)
    else:
        print("Invalid choice.")
        sys.exit(1)
    
    print(f"\nWill check Blue{start_num:04d} to Blue{end_num:04d}")
    print(f"Total gamertags: {end_num - start_num + 1}")
    
    # Estimate time with improved rate limiting
    estimated_minutes = (end_num - start_num + 1) * 2.5 / 60  # 2.5 seconds per request
    print(f"Estimated time: {estimated_minutes:.1f} minutes")
    
    confirm = input("\nProceed? (y/n): ").strip().lower()
    if confirm != 'y':
        print("Cancelled.")
        return
    
    # Get API key
    api_key = get_api_key()
    
    # Import and run the tool
    try:
        from xbox_gamertag_lookup import GamertagLookupTool
    except ImportError:
        print("Error: Could not import xbox_gamertag_lookup.py")
        sys.exit(1)
    
    print(f"\nStarting lookup from Blue{start_num:04d} to Blue{end_num:04d}...")
    print("Note: Improved rate limiting will prevent API errors.")
    print("=" * 60)
    
    tool = GamertagLookupTool(api_key)
    
    try:
        minecraft_owners = tool.batch_lookup(start_num, end_num)
        
        # Load existing results if any
        existing_results = []
        if results_file and os.path.exists(results_file):
            try:
                with open(results_file, 'r') as f:
                    existing_results = json.load(f)
            except:
                pass
        
        # Combine results
        all_results = existing_results + [
            {
                'gamertag': result.gamertag,
                'xuid': result.xuid,
                'last_seen': result.last_seen,
                'has_minecraft_java': result.has_minecraft_java
            }
            for result in minecraft_owners
        ]
        
        # Save combined results
        output_file = f"minecraft_java_owners_0000_{end_num:04d}.json"
        with open(output_file, 'w') as f:
            json.dump(all_results, f, indent=2)
        
        print(f"\n" + "=" * 80)
        print("LOOKUP COMPLETED!")
        print("=" * 80)
        print(f"New Minecraft Java Edition owners found: {len(minecraft_owners)}")
        print(f"Total Minecraft Java Edition owners: {len(all_results)}")
        print(f"Results saved to: {output_file}")
        
        if minecraft_owners:
            print(f"\nNew owners found in this batch:")
            for result in minecraft_owners:
                print(f"  - {result.gamertag} (XUID: {result.xuid})")
        
    except KeyboardInterrupt:
        print("\n\nLookup interrupted by user.")
        partial_owners = [r for r in tool.results if r.has_minecraft_java]
        if partial_owners:
            print(f"Partial results: {len(partial_owners)} new owners found")
            
            # Save partial results
            partial_file = f"partial_minecraft_java_owners_{start_num:04d}_{end_num:04d}.json"
            partial_data = [
                {
                    'gamertag': result.gamertag,
                    'xuid': result.xuid,
                    'last_seen': result.last_seen,
                    'has_minecraft_java': result.has_minecraft_java
                }
                for result in partial_owners
            ]
            
            with open(partial_file, 'w') as f:
                json.dump(partial_data, f, indent=2)
            
            print(f"Partial results saved to: {partial_file}")

if __name__ == "__main__":
    main()
