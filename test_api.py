#!/usr/bin/env python3
"""
Test script to verify OpenXBL API connectivity and functionality
"""

import requests
import sys

def test_api_key(api_key):
    """Test if the API key works by making a simple request"""
    
    headers = {
        'X-Authorization': api_key,
        'Content-Type': 'application/json'
    }
    
    try:
        # Test with a simple account request
        response = requests.get('https://xbl.io/api/v2/account', headers=headers)
        
        if response.status_code == 200:
            print("✓ API key is valid!")
            data = response.json()
            if 'profileUsers' in data and len(data['profileUsers']) > 0:
                profile = data['profileUsers'][0]
                gamertag = None
                for setting in profile.get('settings', []):
                    if setting.get('id') == 'Gamertag':
                        gamertag = setting.get('value')
                        break
                
                print(f"✓ Connected as: {gamertag or 'Unknown'}")
                print(f"✓ XUID: {profile.get('id', 'Unknown')}")
            return True
            
        elif response.status_code == 401:
            print("✗ API key is invalid or expired")
            return False
        else:
            print(f"✗ API Error: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ Connection error: {e}")
        return False

def test_gamertag_search(api_key, test_gamertag="MajorNelson"):
    """Test gamertag search functionality"""
    
    headers = {
        'X-Authorization': api_key,
        'Content-Type': 'application/json'
    }
    
    try:
        url = f'https://xbl.io/api/v2/search/{test_gamertag}'
        response = requests.get(url, headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            if data and 'people' in data and len(data['people']) > 0:
                person = data['people'][0]
                print(f"✓ Gamertag search works!")
                print(f"  Found: {person.get('gamertag', 'Unknown')}")
                print(f"  XUID: {person.get('xuid', 'Unknown')}")
                print(f"  Last seen: {person.get('lastSeenDateTimeUtc', 'Unknown')}")
                return True
            else:
                print(f"✗ No results found for {test_gamertag}")
                return False
        else:
            print(f"✗ Search API Error: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ Search error: {e}")
        return False

def main():
    print("OpenXBL API Test Script")
    print("=" * 40)
    
    # Get API key from user
    api_key = input("Enter your OpenXBL API key: ").strip()
    
    if not api_key:
        print("No API key provided. Exiting.")
        sys.exit(1)
    
    print("\nTesting API connectivity...")
    
    # Test API key
    if not test_api_key(api_key):
        print("\nAPI key test failed. Please check your key and try again.")
        print("\nTo get an API key:")
        print("1. Go to https://xbl.io/")
        print("2. Click 'Login with Xbox Live'")
        print("3. Sign in with your Microsoft/Xbox account")
        print("4. Go to your profile to get your API key")
        sys.exit(1)
    
    print("\nTesting gamertag search...")
    
    # Test gamertag search
    if test_gamertag_search(api_key):
        print("\n✓ All tests passed! Your API key is working correctly.")
        print("\nYou can now use this API key in the main xbox_gamertag_lookup.py script.")
        print(f"Replace 'YOUR_OPENXBL_API_KEY_HERE' with: {api_key}")
    else:
        print("\n✗ Gamertag search test failed.")
        print("The API key works but there may be issues with the search functionality.")

if __name__ == "__main__":
    main()
