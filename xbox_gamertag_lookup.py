#!/usr/bin/env python3
"""
Xbox Gamertag Lookup Tool for Minecraft Java Edition Ownership
Systematically checks gamertags from Blue0000 to Blue9999 for Minecraft Java Edition ownership.
"""

import requests
import time
import json
import sys
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime

@dataclass
class GamertagResult:
    """Data class to store gamertag lookup results"""
    gamertag: str
    xuid: Optional[str] = None
    exists: bool = False
    has_minecraft_java: bool = False
    last_seen: Optional[str] = None
    error: Optional[str] = None

class XboxAPIClient:
    """Client for interacting with OpenXBL API"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://xbl.io/api/v2"
        self.session = requests.Session()
        self.session.headers.update({
            'X-Authorization': api_key,
            'Content-Type': 'application/json'
        })
        
        # Rate limiting - OpenXBL has multiple limits:
        # - 10 requests per 15 seconds
        # - 30 requests per 5 minutes (300 seconds)
        self.last_request_time = 0
        self.min_request_interval = 8.0  # 8 seconds between requests - ultra conservative
        self.request_count_15s = 0
        self.request_count_300s = 0
        self.window_15s_start = 0
        self.window_300s_start = 0
        
    def _rate_limit(self):
        """Implement rate limiting to avoid being blocked"""
        current_time = time.time()

        # Reset counters if windows have expired
        if current_time - self.window_15s_start >= 15:
            self.request_count_15s = 0
            self.window_15s_start = current_time

        if current_time - self.window_300s_start >= 300:
            self.request_count_300s = 0
            self.window_300s_start = current_time

        # Check if we need to wait for 15-second window
        if self.request_count_15s >= 3:  # Very conservative - only 3 requests per 15 seconds
            wait_time = 15 - (current_time - self.window_15s_start)
            if wait_time > 0:
                print(f"Rate limit: waiting {wait_time:.1f}s for 15-second window...")
                time.sleep(wait_time + 1)
                self.request_count_15s = 0
                self.window_15s_start = time.time()

        # Check if we need to wait for 5-minute window
        if self.request_count_300s >= 15:  # Very conservative - only 15 requests per 5 minutes
            wait_time = 300 - (current_time - self.window_300s_start)
            if wait_time > 0:
                print(f"Rate limit: waiting {wait_time:.1f}s for 5-minute window...")
                time.sleep(wait_time + 1)
                self.request_count_300s = 0
                self.window_300s_start = time.time()

        # Basic interval between requests
        time_since_last = current_time - self.last_request_time
        if time_since_last < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last
            time.sleep(sleep_time)

        # Update counters and timestamp
        self.last_request_time = time.time()
        self.request_count_15s += 1
        self.request_count_300s += 1
    
    def search_gamertag(self, gamertag: str) -> Optional[Dict]:
        """Search for a gamertag and return profile information"""
        self._rate_limit()
        
        try:
            url = f"{self.base_url}/search/{gamertag}"
            response = self.session.get(url)
            
            if response.status_code == 200:
                data = response.json()
                if data and 'people' in data and len(data['people']) > 0:
                    # Find exact match
                    for person in data['people']:
                        if person.get('gamertag', '').lower() == gamertag.lower():
                            return person
                return None
            elif response.status_code == 404:
                return None
            else:
                print(f"API Error for {gamertag}: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"Exception searching for {gamertag}: {e}")
            return None
    
    def get_title_history(self, xuid: str) -> Optional[List[Dict]]:
        """Get the title history for a user by XUID"""
        self._rate_limit()
        
        try:
            url = f"{self.base_url}/player/titleHistory/{xuid}"
            response = self.session.get(url)
            
            if response.status_code == 200:
                data = response.json()
                return data.get('titles', [])
            else:
                print(f"API Error getting title history for XUID {xuid}: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"Exception getting title history for XUID {xuid}: {e}")
            return None

class MinecraftDetector:
    """Logic for detecting Minecraft Java Edition ownership"""
    
    # Known Minecraft title IDs and names (these may need to be updated)
    MINECRAFT_IDENTIFIERS = [
        'minecraft',
        'minecraft: java edition',
        'minecraft java edition',
        'minecraft: java & bedrock edition',
        'minecraft java & bedrock edition'
    ]
    
    @staticmethod
    def has_minecraft_java(titles: List[Dict]) -> bool:
        """
        Determine if the user owns Minecraft Java Edition based on title history
        This is challenging as Xbox Live may not clearly distinguish between Java and Bedrock
        """
        if not titles:
            return False
            
        for title in titles:
            title_name = title.get('name', '').lower()
            
            # Look for Minecraft-related titles
            for identifier in MinecraftDetector.MINECRAFT_IDENTIFIERS:
                if identifier in title_name:
                    # Additional logic could be added here to distinguish Java vs Bedrock
                    # For now, we'll consider any Minecraft title as potential Java Edition
                    return True
                    
        return False

class GamertagLookupTool:
    """Main tool for looking up gamertags and checking Minecraft Java Edition ownership"""
    
    def __init__(self, api_key: str):
        self.api_client = XboxAPIClient(api_key)
        self.detector = MinecraftDetector()
        self.results: List[GamertagResult] = []
        
    def lookup_gamertag(self, gamertag: str) -> GamertagResult:
        """Lookup a single gamertag and check for Minecraft Java Edition ownership"""
        result = GamertagResult(gamertag=gamertag)
        
        print(f"Checking {gamertag}...", end=" ")
        
        # Search for the gamertag
        profile = self.api_client.search_gamertag(gamertag)
        
        if not profile:
            print("Not found")
            return result
            
        result.exists = True
        result.xuid = profile.get('xuid')
        result.last_seen = profile.get('lastSeenDateTimeUtc')
        
        # Get title history to check for Minecraft
        if result.xuid:
            titles = self.api_client.get_title_history(result.xuid)
            if titles is not None:
                result.has_minecraft_java = self.detector.has_minecraft_java(titles)
                
        if result.has_minecraft_java:
            print("✓ HAS MINECRAFT JAVA EDITION")
        else:
            print("No Minecraft Java Edition")
            
        return result
    
    def batch_lookup(self, start_num: int = 0, end_num: int = 9999) -> List[GamertagResult]:
        """Perform batch lookup of gamertags from Blue{start_num} to Blue{end_num}"""
        print(f"Starting batch lookup from Blue{start_num:04d} to Blue{end_num:04d}")
        print(f"Total gamertags to check: {end_num - start_num + 1}")
        print("=" * 60)
        
        minecraft_owners = []
        
        for i in range(start_num, end_num + 1):
            gamertag = f"Blue{i:04d}"
            result = self.lookup_gamertag(gamertag)
            self.results.append(result)
            
            if result.has_minecraft_java:
                minecraft_owners.append(result)
                
            # Progress update every 100 gamertags
            if (i - start_num + 1) % 100 == 0:
                progress = i - start_num + 1
                total = end_num - start_num + 1
                found = len(minecraft_owners)
                print(f"\nProgress: {progress}/{total} ({progress/total*100:.1f}%) - Found {found} Minecraft Java Edition owners")
                print("-" * 60)
                
        return minecraft_owners
    
    def display_results(self, minecraft_owners: List[GamertagResult]):
        """Display the final results"""
        print("\n" + "=" * 80)
        print("FINAL RESULTS - GAMERTAGS WITH MINECRAFT JAVA EDITION")
        print("=" * 80)
        
        if not minecraft_owners:
            print("No gamertags found with Minecraft Java Edition ownership.")
            return
            
        print(f"Found {len(minecraft_owners)} gamertags with Minecraft Java Edition:")
        print()
        
        for result in minecraft_owners:
            print(f"Gamertag: {result.gamertag}")
            if result.last_seen:
                print(f"  Last seen: {result.last_seen}")
            if result.xuid:
                print(f"  XUID: {result.xuid}")
            print()
    
    def save_results_to_file(self, minecraft_owners: List[GamertagResult], filename: str = "minecraft_java_owners.json"):
        """Save results to a JSON file"""
        data = []
        for result in minecraft_owners:
            data.append({
                'gamertag': result.gamertag,
                'xuid': result.xuid,
                'last_seen': result.last_seen,
                'has_minecraft_java': result.has_minecraft_java
            })
            
        with open(filename, 'w') as f:
            json.dump(data, f, indent=2)
            
        print(f"Results saved to {filename}")

def main():
    """Main function"""
    # You need to get an API key from https://xbl.io/
    # For now, we'll use a placeholder - user needs to replace this
    API_KEY = "YOUR_OPENXBL_API_KEY_HERE"
    
    if API_KEY == "YOUR_OPENXBL_API_KEY_HERE":
        print("ERROR: Please set your OpenXBL API key in the script.")
        print("Get your API key from: https://xbl.io/")
        print("Then replace 'YOUR_OPENXBL_API_KEY_HERE' with your actual key.")
        sys.exit(1)
    
    tool = GamertagLookupTool(API_KEY)
    
    # For testing, start with a smaller range
    # Change these values to run the full 0-9999 range
    START_NUM = 0
    END_NUM = 99  # Start with first 100 for testing
    
    try:
        minecraft_owners = tool.batch_lookup(START_NUM, END_NUM)
        tool.display_results(minecraft_owners)
        tool.save_results_to_file(minecraft_owners)
        
    except KeyboardInterrupt:
        print("\n\nLookup interrupted by user.")
        print(f"Partial results found so far: {len([r for r in tool.results if r.has_minecraft_java])} Minecraft Java Edition owners")
        
        # Display partial results
        partial_owners = [r for r in tool.results if r.has_minecraft_java]
        if partial_owners:
            tool.display_results(partial_owners)
            tool.save_results_to_file(partial_owners, "partial_minecraft_java_owners.json")

if __name__ == "__main__":
    main()
