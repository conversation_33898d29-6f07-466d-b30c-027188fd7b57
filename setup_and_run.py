#!/usr/bin/env python3
"""
Setup and run script for Xbox Gamertag Lookup Tool
This script helps users configure their API key and run the lookup tool.
"""

import os
import sys
import json
from pathlib import Path

def get_api_key():
    """Get API key from user input or config file"""
    config_file = Path("config.json")
    
    # Try to load from config file first
    if config_file.exists():
        try:
            with open(config_file, 'r') as f:
                config = json.load(f)
                if 'api_key' in config and config['api_key']:
                    return config['api_key']
        except:
            pass
    
    # Get from user input
    print("OpenXBL API Key Setup")
    print("=" * 30)
    print("\nTo get your API key:")
    print("1. Go to https://xbl.io/")
    print("2. Click 'Login with Xbox Live'")
    print("3. Sign in with your Microsoft/Xbox account")
    print("4. Go to your profile to get your API key")
    print()
    
    api_key = input("Enter your OpenXBL API key: ").strip()
    
    if not api_key:
        print("No API key provided. Exiting.")
        sys.exit(1)
    
    # Save to config file
    config = {'api_key': api_key}
    with open(config_file, 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"API key saved to {config_file}")
    return api_key

def get_range_settings():
    """Get the range of gamertags to check"""
    print("\nGamertag Range Settings")
    print("=" * 25)
    print("Choose the range of gamertags to check:")
    print("1. Test run (Blue0000 - Blue0099) - ~100 gamertags")
    print("2. Small batch (Blue0000 - Blue0999) - ~1,000 gamertags") 
    print("3. Full run (Blue0000 - Blue9999) - ~10,000 gamertags")
    print("4. Custom range")
    
    while True:
        choice = input("\nEnter your choice (1-4): ").strip()
        
        if choice == "1":
            return 0, 99
        elif choice == "2":
            return 0, 999
        elif choice == "3":
            return 0, 9999
        elif choice == "4":
            try:
                start = int(input("Enter start number (0-9999): "))
                end = int(input("Enter end number (0-9999): "))
                if 0 <= start <= end <= 9999:
                    return start, end
                else:
                    print("Invalid range. Please enter numbers between 0 and 9999.")
            except ValueError:
                print("Please enter valid numbers.")
        else:
            print("Invalid choice. Please enter 1, 2, 3, or 4.")

def run_lookup_tool(api_key, start_num, end_num):
    """Run the main lookup tool with the specified parameters"""
    
    # Import the main tool
    try:
        from xbox_gamertag_lookup import GamertagLookupTool
    except ImportError:
        print("Error: Could not import xbox_gamertag_lookup.py")
        print("Make sure the file is in the same directory.")
        sys.exit(1)
    
    print(f"\nStarting lookup for Blue{start_num:04d} to Blue{end_num:04d}")
    print(f"Total gamertags to check: {end_num - start_num + 1}")
    
    estimated_time = (end_num - start_num + 1) * 1.2 / 60  # 1.2 seconds per request
    print(f"Estimated time: {estimated_time:.1f} minutes")
    
    confirm = input("\nProceed? (y/n): ").strip().lower()
    if confirm != 'y':
        print("Cancelled.")
        return
    
    # Create and run the tool
    tool = GamertagLookupTool(api_key)
    
    try:
        minecraft_owners = tool.batch_lookup(start_num, end_num)
        tool.display_results(minecraft_owners)
        
        # Save results
        filename = f"minecraft_java_owners_{start_num:04d}_{end_num:04d}.json"
        tool.save_results_to_file(minecraft_owners, filename)
        
        print(f"\nLookup completed successfully!")
        print(f"Results saved to: {filename}")
        
    except KeyboardInterrupt:
        print("\n\nLookup interrupted by user.")
        partial_owners = [r for r in tool.results if r.has_minecraft_java]
        print(f"Partial results: {len(partial_owners)} Minecraft Java Edition owners found")
        
        if partial_owners:
            tool.display_results(partial_owners)
            filename = f"partial_minecraft_java_owners_{start_num:04d}_{end_num:04d}.json"
            tool.save_results_to_file(partial_owners, filename)
            print(f"Partial results saved to: {filename}")

def main():
    """Main function"""
    print("Xbox Gamertag Lookup Tool - Setup and Run")
    print("=" * 45)
    
    # Check if dependencies are installed
    try:
        import requests
    except ImportError:
        print("Error: 'requests' library not found.")
        print("Please install it with: pip install requests")
        sys.exit(1)
    
    # Get API key
    api_key = get_api_key()
    
    # Test API key first
    print("\nTesting API key...")
    try:
        from test_api import test_api_key
        if not test_api_key(api_key):
            print("API key test failed. Please check your key.")
            sys.exit(1)
    except ImportError:
        print("Warning: Could not test API key (test_api.py not found)")
    
    # Get range settings
    start_num, end_num = get_range_settings()
    
    # Run the lookup tool
    run_lookup_tool(api_key, start_num, end_num)

if __name__ == "__main__":
    main()
