"""
Ultra-conservative resume script with better resource management
"""
import time
import gc
import psutil
import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from kali_lookup import KaliGamertagLookupTool, load_config, logger

def monitor_resources():
    """Monitor system resources"""
    memory = psutil.virtual_memory()
    cpu = psutil.cpu_percent(interval=1)
    
    logger.info(f"Memory: {memory.percent}% used, CPU: {cpu}%")
    
    # If memory usage is high, force garbage collection
    if memory.percent > 80:
        logger.warning("High memory usage detected. Running garbage collection...")
        gc.collect()
        time.sleep(5)
    
    # If CPU is very high, slow down
    if cpu > 90:
        logger.warning("High CPU usage detected. Slowing down...")
        time.sleep(10)

def conservative_batch_lookup(tool, start_num, end_num, batch_size=50):
    """Run lookup in smaller batches with resource monitoring"""
    
    current = start_num
    all_owners = []
    
    while current <= end_num:
        batch_end = min(current + batch_size - 1, end_num)
        
        logger.info(f"Processing batch: Blue{current:04d} to Blue{batch_end:04d}")
        
        # Monitor resources before each batch
        monitor_resources()
        
        # Process batch
        owners = tool.batch_lookup(current, batch_end)
        all_owners.extend(owners)
        
        # Save progress after each batch
        tool.save_progress(batch_end)
        
        # Force garbage collection and brief pause between batches
        gc.collect()
        time.sleep(5)
        
        current = batch_end + 1
    
    return all_owners

def main():
    config = load_config()
    api_key = config.get('api_key')
    
    if not api_key:
        logger.error("No API key found in config.json")
        sys.exit(1)
    
    # Start from Blue3195
    start_num = 3195
    end_num = 9999
    
    logger.info(f"Conservative resume from Blue{start_num:04d} to Blue{end_num:04d}")
    
    tool = KaliGamertagLookupTool(api_key)
    
    try:
        owners = conservative_batch_lookup(tool, start_num, end_num)
        logger.info(f"Completed! Found {len(owners)} total owners")
    except Exception as e:
        logger.error(f"Error: {e}")
        tool.save_progress(start_num)

if __name__ == "__main__":
    main()