# Xbox Gamertag Lookup Tool - Complete Instructions

## What This Tool Does

This tool systematically checks Xbox gamertags from "Blue0000" to "Blue9999" (10,000 total) to find which accounts own Minecraft Java Edition. It will:

1. Search for each gamertag using Xbox Live API
2. Check if the account exists
3. If it exists, check their game library for Minecraft Java Edition
4. Display results showing only accounts that own Minecraft Java Edition
5. Include last online dates when available
6. Save results to a JSON file

## Files Included

- `xbox_gamertag_lookup.py` - Main lookup tool
- `setup_and_run.py` - Interactive setup and execution script (RECOMMENDED)
- `test_api.py` - API key testing script
- `requirements.txt` - Python dependencies
- `README.md` - Detailed documentation
- `INSTRUCTIONS.md` - This file

## Step-by-Step Instructions

### Step 1: Install Python Dependencies
```bash
pip install requests
```

### Step 2: Get Your OpenXBL API Key

1. **Go to https://xbl.io/**
2. **Click "Login with Xbox Live"**
3. **Sign in with your Microsoft/Xbox account**
4. **Navigate to your profile/dashboard**
5. **Copy your API key** (it will look like a long string of letters and numbers)

**Important:** OpenXBL provides 150 free API requests per hour. This is enough for testing but you may need to run the full scan in batches.

### Step 3: Run the Tool

#### Option A: Interactive Setup (EASIEST)
```bash
python setup_and_run.py
```

This will:
- Ask for your API key and save it
- Test the API key
- Let you choose how many gamertags to check
- Run the lookup automatically

#### Option B: Manual Setup
1. Test your API key first:
   ```bash
   python test_api.py
   ```

2. Edit `xbox_gamertag_lookup.py` and replace:
   ```python
   API_KEY = "YOUR_OPENXBL_API_KEY_HERE"
   ```
   With your actual API key.

3. Run the tool:
   ```bash
   python xbox_gamertag_lookup.py
   ```

## Recommended Approach

### For Testing (Start Here)
- Use the interactive setup: `python setup_and_run.py`
- Choose option 1 (Blue0000 - Blue0099) for testing
- This checks 100 gamertags and takes about 2-3 minutes

### For Full Scan
- The full scan (Blue0000 - Blue9999) checks 10,000 gamertags
- **Estimated time: 3-4 hours** due to rate limiting
- **API requests needed: ~20,000** (2 requests per gamertag)
- You may need to run this in batches due to API limits

### Batch Processing Strategy
If you want to check all 10,000 gamertags:

1. **Run in smaller batches** (e.g., 1000 at a time)
2. **Use the interactive setup** and choose "Custom range"
3. **Example batches:**
   - Batch 1: Blue0000 - Blue0999
   - Batch 2: Blue1000 - Blue1999
   - Batch 3: Blue2000 - Blue2999
   - etc.

## Expected Results

The tool will output:
- Progress updates as it checks each gamertag
- Real-time notifications when it finds Minecraft Java Edition owners
- Final summary of all found accounts
- JSON file with detailed results

**Example output:**
```
Checking Blue0000... Not found
Checking Blue0001... No Minecraft Java Edition
Checking Blue0002... ✓ HAS MINECRAFT JAVA EDITION
...

FINAL RESULTS - GAMERTAGS WITH MINECRAFT JAVA EDITION
================================================================================
Found 3 gamertags with Minecraft Java Edition:

Gamertag: Blue0002
  Last seen: 2023-12-15T10:30:00Z
  XUID: ****************
```

## Important Limitations

### Minecraft Detection Accuracy
- The tool detects Minecraft through Xbox Live title history
- It may not perfectly distinguish between Java Edition and Bedrock Edition
- Some Java Edition owners might not be detected if they haven't linked their accounts
- Results should be considered estimates, not definitive

### API Limitations
- 150 requests per hour with free OpenXBL account
- Full scan requires ~20,000 requests (2 per gamertag)
- May need to run in batches over multiple hours/days
- Some gamertags may not be found even if they exist

### Rate Limiting
- Tool implements 1-second delays between requests
- This prevents being blocked but makes the process slow
- Do not reduce the delay or you may get blocked

## Troubleshooting

### "API Error" Messages
- Check your API key is correct
- Verify internet connection
- API may be temporarily unavailable

### "No results found"
- Many gamertags in Blue0000-Blue9999 range may not exist
- This is normal and expected

### Rate Limiting Issues
- If you get blocked, wait an hour and try again
- Consider increasing delays in the script
- Run smaller batches

## Legal and Ethical Considerations

- This tool uses an unofficial Xbox Live API
- Respect Xbox Live Terms of Service
- Use responsibly and don't abuse the API
- Consider the privacy of the accounts you're looking up
- This is for research/informational purposes only

## Support

If you encounter issues:
1. Check the README.md for detailed documentation
2. Test your API key with `test_api.py`
3. Start with small test batches before running full scans
4. Ensure you have a stable internet connection
