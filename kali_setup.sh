#!/bin/bash
# Kali Linux Setup Script for Xbox Gamertag Lookup Tool

echo "Xbox Gamertag Lookup Tool - Kali Linux Setup"
echo "============================================="

# Update system
echo "Updating system packages..."
sudo apt update

# Install Python3 and pip if not already installed
echo "Installing Python3 and pip..."
sudo apt install -y python3 python3-pip python3-venv

# Install required Python packages
echo "Installing Python dependencies..."
pip3 install requests

# Create project directory
echo "Creating project directory..."
mkdir -p ~/xbox-gamertag-lookup
cd ~/xbox-gamertag-lookup

echo ""
echo "Setup complete!"
echo "==============="
echo "Next steps:"
echo "1. Copy your Python scripts to ~/xbox-gamertag-lookup/"
echo "2. Copy your config.json with API key"
echo "3. Copy your existing results files"
echo "4. Run: python3 kali_lookup.py"
echo ""
echo "To run in background: nohup python3 kali_lookup.py > lookup.log 2>&1 &"
echo "To monitor progress: tail -f lookup.log"
