#!/usr/bin/env python3
"""
Analyze Xbox Gamertag Lookup Results
"""

import json
import os
from pathlib import Path

def analyze_results(filename):
    """Analyze the results from a JSON file"""
    
    if not os.path.exists(filename):
        print(f"File {filename} not found.")
        return
    
    try:
        with open(filename, 'r') as f:
            data = json.load(f)
    except Exception as e:
        print(f"Error reading file: {e}")
        return
    
    if not data:
        print("No results found in file.")
        return
    
    print(f"Analysis of {filename}")
    print("=" * 50)
    
    # Basic stats
    total_found = len(data)
    print(f"Total Minecraft Java Edition owners found: {total_found}")
    
    # Find range
    numbers = []
    for entry in data:
        gamertag = entry.get('gamertag', '')
        if gamertag.startswith('Blue'):
            try:
                num = int(gamertag[4:])
                numbers.append(num)
            except ValueError:
                continue
    
    if numbers:
        min_num = min(numbers)
        max_num = max(numbers)
        range_checked = max_num - min_num + 1
        
        print(f"Range: Blue{min_num:04d} to Blue{max_num:04d}")
        print(f"Last checked: Blue{max_num:04d}")
        print(f"Hit rate: {total_found}/{range_checked} ({total_found/range_checked*100:.1f}%)")
        
        # Show progress
        if max_num < 9999:
            remaining = 9999 - max_num
            print(f"Remaining to check: {remaining} gamertags (Blue{max_num+1:04d} to Blue9999)")
            
            # Estimate total based on current hit rate
            if range_checked > 0:
                estimated_total = (total_found / range_checked) * 10000
                print(f"Estimated total for full range: ~{estimated_total:.0f} owners")
    
    print("\nFound gamertags:")
    print("-" * 30)
    
    # Sort by number
    sorted_data = sorted(data, key=lambda x: int(x['gamertag'][4:]) if x['gamertag'].startswith('Blue') else 0)
    
    for entry in sorted_data:
        gamertag = entry.get('gamertag', 'Unknown')
        xuid = entry.get('xuid', 'Unknown')
        last_seen = entry.get('last_seen', 'Unknown')
        
        print(f"{gamertag}")
        print(f"  XUID: {xuid}")
        if last_seen and last_seen != 'Unknown':
            print(f"  Last seen: {last_seen}")
        print()

def main():
    print("Xbox Gamertag Results Analyzer")
    print("=" * 35)
    
    # Find available result files
    result_files = []
    for file in Path('.').glob('*minecraft*owners*.json'):
        result_files.append(str(file))
    
    if not result_files:
        print("No result files found.")
        return
    
    if len(result_files) == 1:
        analyze_results(result_files[0])
    else:
        print("Multiple result files found:")
        for i, file in enumerate(result_files, 1):
            print(f"{i}. {file}")
        
        try:
            choice = int(input(f"\nChoose file to analyze (1-{len(result_files)}): "))
            if 1 <= choice <= len(result_files):
                analyze_results(result_files[choice-1])
            else:
                print("Invalid choice.")
        except ValueError:
            print("Invalid input.")

if __name__ == "__main__":
    main()
