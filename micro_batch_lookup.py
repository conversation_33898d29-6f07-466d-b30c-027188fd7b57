#!/usr/bin/env python3
"""
Micro-batch Xbox Gamertag Lookup Tool
Runs very small batches to stay well within API limits.
"""

import json
import os
import sys
import time
from pathlib import Path

def find_last_checked():
    """Find where we left off"""
    files = [
        "partial_minecraft_java_owners_0057_9999.json",
        "partial_minecraft_java_owners_0000_9999.json",
        "minecraft_java_owners.json"
    ]
    
    max_num = -1
    for file in files:
        if os.path.exists(file):
            try:
                with open(file, 'r') as f:
                    data = json.load(f)
                for entry in data:
                    gamertag = entry.get('gamertag', '')
                    if gamertag.startswith('Blue'):
                        try:
                            num = int(gamertag[4:])
                            max_num = max(max_num, num)
                        except ValueError:
                            continue
            except:
                continue
    
    return max_num + 1 if max_num >= 0 else 0

def get_api_key():
    """Get API key"""
    config_file = Path("config.json")
    if config_file.exists():
        try:
            with open(config_file, 'r') as f:
                config = json.load(f)
                if 'api_key' in config:
                    return config['api_key']
        except:
            pass
    
    return input("Enter your OpenXBL API key: ").strip()

def run_micro_batch(api_key, start_num, batch_size=10):
    """Run a very small batch"""
    
    try:
        from xbox_gamertag_lookup import GamertagLookupTool
    except ImportError:
        print("Error: Could not import xbox_gamertag_lookup.py")
        return []
    
    end_num = min(start_num + batch_size - 1, 9999)
    
    print(f"Micro-batch: Blue{start_num:04d} to Blue{end_num:04d} ({end_num - start_num + 1} gamertags)")
    print("Ultra-conservative rate limiting: 8 seconds between requests")
    
    estimated_time = (end_num - start_num + 1) * 8 / 60
    print(f"Estimated time: {estimated_time:.1f} minutes")
    print("-" * 50)
    
    tool = GamertagLookupTool(api_key)
    
    try:
        minecraft_owners = tool.batch_lookup(start_num, end_num)
        return minecraft_owners
    except KeyboardInterrupt:
        print("\nInterrupted by user.")
        return [r for r in tool.results if r.has_minecraft_java]

def save_results(new_results, start_num):
    """Save results, combining with existing data"""
    
    # Load all existing results
    all_results = []
    files = [
        "partial_minecraft_java_owners_0000_9999.json",
        "partial_minecraft_java_owners_0057_9999.json",
        "minecraft_java_owners.json"
    ]
    
    for file in files:
        if os.path.exists(file):
            try:
                with open(file, 'r') as f:
                    data = json.load(f)
                    all_results.extend(data)
            except:
                continue
    
    # Add new results
    for result in new_results:
        all_results.append({
            'gamertag': result.gamertag,
            'xuid': result.xuid,
            'last_seen': result.last_seen,
            'has_minecraft_java': result.has_minecraft_java
        })
    
    # Remove duplicates and sort
    seen = set()
    unique_results = []
    for result in all_results:
        gamertag = result['gamertag']
        if gamertag not in seen:
            seen.add(gamertag)
            unique_results.append(result)
    
    # Sort by gamertag number
    unique_results.sort(key=lambda x: int(x['gamertag'][4:]) if x['gamertag'].startswith('Blue') else 0)
    
    # Save to main file
    output_file = "minecraft_java_owners_combined.json"
    with open(output_file, 'w') as f:
        json.dump(unique_results, f, indent=2)
    
    return output_file, len(unique_results)

def main():
    print("Xbox Gamertag Lookup - Micro-Batch Mode")
    print("=" * 45)
    print("This mode runs very small batches to avoid API limits.")
    print()
    
    # Find where we left off
    start_num = find_last_checked()
    print(f"Will start from: Blue{start_num:04d}")
    
    if start_num > 9999:
        print("All gamertags have been checked!")
        return
    
    # Get batch size
    print("\nChoose micro-batch size:")
    print("1. Tiny (5 gamertags) - ~1 minute")
    print("2. Small (10 gamertags) - ~2 minutes") 
    print("3. Medium (20 gamertags) - ~3 minutes")
    print("4. Custom")
    
    choice = input("Enter choice (1-4): ").strip()
    
    if choice == "1":
        batch_size = 5
    elif choice == "2":
        batch_size = 10
    elif choice == "3":
        batch_size = 20
    elif choice == "4":
        try:
            batch_size = int(input("Enter batch size (1-50): "))
            if batch_size < 1 or batch_size > 50:
                print("Invalid batch size.")
                return
        except ValueError:
            print("Invalid number.")
            return
    else:
        print("Invalid choice.")
        return
    
    # Get API key
    api_key = get_api_key()
    if not api_key:
        print("No API key provided.")
        return
    
    print(f"\nRunning micro-batch of {batch_size} gamertags...")
    print("Note: You can run this script multiple times to continue.")
    print()
    
    # Run the batch
    new_results = run_micro_batch(api_key, start_num, batch_size)
    
    if new_results:
        print(f"\n✓ Found {len(new_results)} new Minecraft Java Edition owners!")
        for result in new_results:
            print(f"  - {result.gamertag}")
    else:
        print("\nNo new Minecraft Java Edition owners found in this batch.")
    
    # Save results
    output_file, total_count = save_results(new_results, start_num)
    
    print(f"\nResults saved to: {output_file}")
    print(f"Total Minecraft Java Edition owners found so far: {total_count}")
    
    # Show next steps
    next_start = start_num + batch_size
    if next_start <= 9999:
        remaining = 9999 - next_start + 1
        print(f"\nNext batch will start at: Blue{next_start:04d}")
        print(f"Remaining gamertags: {remaining}")
        print("\nTo continue, run this script again!")
    else:
        print("\n🎉 All gamertags have been checked!")

if __name__ == "__main__":
    main()
