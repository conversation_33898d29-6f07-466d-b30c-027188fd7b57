#!/usr/bin/env python3
"""
Premium Xbox Gamertag Lookup Tool
Optimized for upgraded OpenXBL accounts (5000 requests/hour)
Still respects timed rate limits but maximizes throughput.
"""

import requests
import time
import json
import sys
import os
from typing import Dict, List, Optional
from dataclasses import dataclass
from pathlib import Path

@dataclass
class GamertagResult:
    """Data class to store gamertag lookup results"""
    gamertag: str
    xuid: Optional[str] = None
    exists: bool = False
    has_minecraft_java: bool = False
    last_seen: Optional[str] = None
    error: Optional[str] = None

class PremiumXboxAPIClient:
    """Optimized client for premium OpenXBL accounts"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://xbl.io/api/v2"
        self.session = requests.Session()
        self.session.headers.update({
            'X-Authorization': api_key,
            'Content-Type': 'application/json'
        })
        
        # Optimized rate limiting for premium accounts
        # Still respect timed limits but be more aggressive
        self.last_request_time = 0
        self.min_request_interval = 1.8  # 1.8 seconds between requests
        self.request_count_15s = 0
        self.request_count_300s = 0
        self.window_15s_start = 0
        self.window_300s_start = 0
        
        # Track hourly usage
        self.hourly_requests = 0
        self.hour_start = time.time()
        
    def _rate_limit(self):
        """Optimized rate limiting for premium accounts"""
        current_time = time.time()
        
        # Reset hourly counter
        if current_time - self.hour_start >= 3600:
            self.hourly_requests = 0
            self.hour_start = current_time
            print(f"Hourly limit reset. Used {self.hourly_requests}/5000 requests this hour.")
        
        # Reset timed windows
        if current_time - self.window_15s_start >= 15:
            self.request_count_15s = 0
            self.window_15s_start = current_time
            
        if current_time - self.window_300s_start >= 300:
            self.request_count_300s = 0
            self.window_300s_start = current_time
        
        # Check hourly limit (should rarely hit with 5000/hour)
        if self.hourly_requests >= 4800:  # Leave some buffer
            wait_time = 3600 - (current_time - self.hour_start)
            if wait_time > 0:
                print(f"Approaching hourly limit. Waiting {wait_time:.1f}s...")
                time.sleep(wait_time + 1)
                self.hourly_requests = 0
                self.hour_start = time.time()
        
        # Check 15-second window (more aggressive than before)
        if self.request_count_15s >= 7:  # Up from 3, still under 10
            wait_time = 15 - (current_time - self.window_15s_start)
            if wait_time > 0:
                print(f"15s rate limit: waiting {wait_time:.1f}s...")
                time.sleep(wait_time + 0.5)
                self.request_count_15s = 0
                self.window_15s_start = time.time()
        
        # Check 5-minute window (more aggressive)
        if self.request_count_300s >= 22:  # Up from 15, still under 30
            wait_time = 300 - (current_time - self.window_300s_start)
            if wait_time > 0:
                print(f"5min rate limit: waiting {wait_time:.1f}s...")
                time.sleep(wait_time + 1)
                self.request_count_300s = 0
                self.window_300s_start = time.time()
        
        # Basic interval between requests
        time_since_last = current_time - self.last_request_time
        if time_since_last < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last
            time.sleep(sleep_time)
            
        # Update counters
        self.last_request_time = time.time()
        self.request_count_15s += 1
        self.request_count_300s += 1
        self.hourly_requests += 1
    
    def search_gamertag(self, gamertag: str) -> Optional[Dict]:
        """Search for a gamertag"""
        self._rate_limit()
        
        try:
            url = f"{self.base_url}/search/{gamertag}"
            response = self.session.get(url)
            
            if response.status_code == 200:
                data = response.json()
                if data and 'people' in data and len(data['people']) > 0:
                    for person in data['people']:
                        if person.get('gamertag', '').lower() == gamertag.lower():
                            return person
                return None
            elif response.status_code == 404:
                return None
            else:
                print(f"API Error for {gamertag}: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"Exception searching for {gamertag}: {e}")
            return None
    
    def get_title_history(self, xuid: str) -> Optional[List[Dict]]:
        """Get title history for a user"""
        self._rate_limit()
        
        try:
            url = f"{self.base_url}/player/titleHistory/{xuid}"
            response = self.session.get(url)
            
            if response.status_code == 200:
                data = response.json()
                return data.get('titles', [])
            else:
                print(f"Title history error for XUID {xuid}: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"Exception getting title history for XUID {xuid}: {e}")
            return None

class MinecraftDetector:
    """Minecraft Java Edition detection logic"""
    
    MINECRAFT_IDENTIFIERS = [
        'minecraft',
        'minecraft: java edition',
        'minecraft java edition',
        'minecraft: java & bedrock edition',
        'minecraft java & bedrock edition'
    ]
    
    @staticmethod
    def has_minecraft_java(titles: List[Dict]) -> bool:
        """Check if user has Minecraft Java Edition"""
        if not titles:
            return False
            
        for title in titles:
            title_name = title.get('name', '').lower()
            for identifier in MinecraftDetector.MINECRAFT_IDENTIFIERS:
                if identifier in title_name:
                    return True
        return False

class PremiumGamertagLookupTool:
    """Premium lookup tool for upgraded accounts"""
    
    def __init__(self, api_key: str):
        self.api_client = PremiumXboxAPIClient(api_key)
        self.detector = MinecraftDetector()
        self.results: List[GamertagResult] = []
        
    def lookup_gamertag(self, gamertag: str) -> GamertagResult:
        """Lookup a single gamertag"""
        result = GamertagResult(gamertag=gamertag)
        
        print(f"Checking {gamertag}...", end=" ")
        
        profile = self.api_client.search_gamertag(gamertag)
        
        if not profile:
            print("Not found")
            return result
            
        result.exists = True
        result.xuid = profile.get('xuid')
        result.last_seen = profile.get('lastSeenDateTimeUtc')
        
        if result.xuid:
            titles = self.api_client.get_title_history(result.xuid)
            if titles is not None:
                result.has_minecraft_java = self.detector.has_minecraft_java(titles)
                
        if result.has_minecraft_java:
            print("✓ HAS MINECRAFT JAVA EDITION")
        else:
            print("No Minecraft Java Edition")
            
        return result
    
    def batch_lookup(self, start_num: int, end_num: int) -> List[GamertagResult]:
        """Premium batch lookup with progress tracking"""
        print(f"Premium lookup: Blue{start_num:04d} to Blue{end_num:04d}")
        print(f"Total: {end_num - start_num + 1} gamertags")
        print(f"Rate: ~1.8s per gamertag (optimized for premium account)")
        
        estimated_time = (end_num - start_num + 1) * 1.8 / 60
        print(f"Estimated time: {estimated_time:.1f} minutes")
        print("=" * 60)
        
        minecraft_owners = []
        
        for i in range(start_num, end_num + 1):
            gamertag = f"Blue{i:04d}"
            result = self.lookup_gamertag(gamertag)
            self.results.append(result)
            
            if result.has_minecraft_java:
                minecraft_owners.append(result)
                
            # Progress updates
            if (i - start_num + 1) % 50 == 0:
                progress = i - start_num + 1
                total = end_num - start_num + 1
                found = len(minecraft_owners)
                hourly_used = self.api_client.hourly_requests
                print(f"\nProgress: {progress}/{total} ({progress/total*100:.1f}%) - Found: {found} - Hourly usage: {hourly_used}/5000")
                print("-" * 60)
                
        return minecraft_owners

def find_last_checked():
    """Find where we left off"""
    files = [
        "partial_minecraft_java_owners_0057_9999.json",
        "partial_minecraft_java_owners_0000_9999.json",
        "minecraft_java_owners_combined.json"
    ]
    
    max_num = 80  # We know we left off at Blue0080
    
    for file in files:
        if os.path.exists(file):
            try:
                with open(file, 'r') as f:
                    data = json.load(f)
                for entry in data:
                    gamertag = entry.get('gamertag', '')
                    if gamertag.startswith('Blue'):
                        try:
                            num = int(gamertag[4:])
                            max_num = max(max_num, num)
                        except ValueError:
                            continue
            except:
                continue
    
    return max_num + 1

def get_api_key():
    """Get API key"""
    config_file = Path("config.json")
    if config_file.exists():
        try:
            with open(config_file, 'r') as f:
                config = json.load(f)
                if 'api_key' in config:
                    return config['api_key']
        except:
            pass
    
    return input("Enter your premium OpenXBL API key: ").strip()

def main():
    print("Premium Xbox Gamertag Lookup Tool")
    print("Optimized for 5000 requests/hour accounts")
    print("=" * 45)
    
    # Find starting point
    start_num = find_last_checked()
    print(f"Will resume from: Blue{start_num:04d}")
    
    if start_num > 9999:
        print("All gamertags checked!")
        return
    
    # Get batch size
    print(f"\nChoose batch size (starting from Blue{start_num:04d}):")
    print("1. Medium (100 gamertags) - ~3 minutes")
    print("2. Large (500 gamertags) - ~15 minutes")
    print("3. Extra Large (1000 gamertags) - ~30 minutes")
    print("4. Complete remaining")
    print("5. Custom range")
    
    choice = input("Enter choice (1-5): ").strip()
    
    if choice == "1":
        end_num = min(start_num + 99, 9999)
    elif choice == "2":
        end_num = min(start_num + 499, 9999)
    elif choice == "3":
        end_num = min(start_num + 999, 9999)
    elif choice == "4":
        end_num = 9999
    elif choice == "5":
        try:
            end_num = int(input(f"Enter ending number ({start_num}-9999): "))
            if end_num < start_num or end_num > 9999:
                print("Invalid range.")
                return
        except ValueError:
            print("Invalid number.")
            return
    else:
        print("Invalid choice.")
        return
    
    # Get API key
    api_key = get_api_key()
    if not api_key:
        print("No API key provided.")
        return
    
    print(f"\nStarting premium lookup: Blue{start_num:04d} to Blue{end_num:04d}")
    confirm = input("Proceed? (y/n): ").strip().lower()
    if confirm != 'y':
        return
    
    # Run lookup
    tool = PremiumGamertagLookupTool(api_key)
    
    try:
        minecraft_owners = tool.batch_lookup(start_num, end_num)
        
        print(f"\n" + "=" * 80)
        print("PREMIUM LOOKUP COMPLETED!")
        print("=" * 80)
        print(f"New Minecraft Java Edition owners found: {len(minecraft_owners)}")
        
        # Save results
        output_file = f"premium_minecraft_owners_{start_num:04d}_{end_num:04d}.json"
        data = [
            {
                'gamertag': result.gamertag,
                'xuid': result.xuid,
                'last_seen': result.last_seen,
                'has_minecraft_java': result.has_minecraft_java
            }
            for result in minecraft_owners
        ]
        
        with open(output_file, 'w') as f:
            json.dump(data, f, indent=2)
        
        print(f"Results saved to: {output_file}")
        
        if minecraft_owners:
            print(f"\nNew owners found:")
            for result in minecraft_owners:
                print(f"  - {result.gamertag} (XUID: {result.xuid})")
        
    except KeyboardInterrupt:
        print("\n\nLookup interrupted.")
        partial_owners = [r for r in tool.results if r.has_minecraft_java]
        if partial_owners:
            print(f"Partial results: {len(partial_owners)} owners found")

if __name__ == "__main__":
    main()
