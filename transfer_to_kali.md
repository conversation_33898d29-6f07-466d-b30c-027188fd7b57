# Transfer Xbox Gamertag Lookup Tool to Kali Linux

## Method 1: Direct File Transfer (Recommended)

### 1. Copy files to Kali VM
You can use any of these methods:

**Option A: Shared folder (if VM has shared folders enabled)**
```bash
# On Windows, copy files to shared folder
# On Kali, access shared folder and copy files
```

**Option B: SCP (if SSH is enabled on Kali)**
```bash
# From Windows (if you have SCP client)
scp *.py *.json *.md kali@<kali-ip>:~/xbox-gamertag-lookup/
```

**Option C: USB drive or network share**
- Copy files to USB drive on Windows
- Mount USB on Kali and copy files

**Option D: Simple copy-paste**
- Copy the file contents from Windows
- Create new files on Kali and paste content

### 2. Files to transfer:
- `kali_lookup.py` (main script for Kali)
- `kali_setup.sh` (setup script)
- `config.json` (your API key)
- All your existing results files:
  - `partial_minecraft_java_owners_0000_9999.json`
  - `partial_minecraft_java_owners_0057_9999.json`

## Method 2: Recreate on Kali

### 1. Run setup on Kali:
```bash
# Make setup script executable
chmod +x kali_setup.sh

# Run setup
./kali_setup.sh
```

### 2. Create config.json with your API key:
```bash
cd ~/xbox-gamertag-lookup
cat > config.json << EOF
{
  "api_key": "YOUR_ACTUAL_API_KEY_HERE"
}
EOF
```

### 3. Create the main script:
```bash
# Copy the kali_lookup.py content to this file
nano kali_lookup.py
```

## Running on Kali Linux

### 1. Test run (foreground):
```bash
cd ~/xbox-gamertag-lookup
python3 kali_lookup.py
```

### 2. Background run (recommended for long processes):
```bash
# Start in background
nohup python3 kali_lookup.py > lookup.log 2>&1 &

# Monitor progress
tail -f lookup.log

# Check if still running
ps aux | grep kali_lookup
```

### 3. Run specific range:
```bash
# Check Blue0159 to Blue1000
python3 kali_lookup.py 1000

# Check Blue0159 to Blue9999 (complete remaining)
python3 kali_lookup.py 9999
```

## Kali Linux Advantages

### 1. **Screen/Tmux sessions** (survive disconnections):
```bash
# Install screen
sudo apt install screen

# Start screen session
screen -S gamertag-lookup

# Run the script
python3 kali_lookup.py

# Detach: Ctrl+A, then D
# Reattach: screen -r gamertag-lookup
```

### 2. **Better process management**:
```bash
# Check running processes
htop

# Kill if needed
pkill -f kali_lookup.py
```

### 3. **Automatic logging**:
- All output goes to `gamertag_lookup.log`
- Progress saved every 25 gamertags
- Graceful shutdown on Ctrl+C

### 4. **Resume capability**:
- Script automatically resumes from Blue0159
- Saves progress regularly
- Can restart if interrupted

## Expected Performance on Kali

With the conservative settings:
- **~3 seconds per gamertag**
- **~20 gamertags per minute**
- **~1,200 gamertags per hour**
- **Blue0159 to Blue9999 (~8,841 gamertags) = ~7.4 hours**

## Monitoring Progress

### 1. Real-time log monitoring:
```bash
tail -f gamertag_lookup.log
```

### 2. Check saved progress files:
```bash
ls -la kali_minecraft_owners_*.json
```

### 3. Count current results:
```bash
# Count lines in latest results file
wc -l kali_minecraft_owners_*.json
```

## Quick Start Commands for Kali

```bash
# 1. Setup
cd ~
mkdir xbox-gamertag-lookup
cd xbox-gamertag-lookup

# 2. Install dependencies
sudo apt update
sudo apt install python3 python3-pip
pip3 install requests

# 3. Create config file (replace with your actual API key)
echo '{"api_key": "YOUR_API_KEY_HERE"}' > config.json

# 4. Create the main script (copy kali_lookup.py content)
nano kali_lookup.py

# 5. Make executable and run
chmod +x kali_lookup.py

# 6. Run in background
nohup python3 kali_lookup.py > lookup.log 2>&1 &

# 7. Monitor
tail -f lookup.log
```

This setup will be much more reliable for the long-running process and should complete the remaining ~8,841 gamertags in about 7-8 hours.
