# Xbox Gamertag Lookup Tool for Minecraft Java Edition

This tool systematically checks Xbox gamertags from "Blue0000" to "Blue9999" to determine which accounts own Minecraft Java Edition.

## Features

- Searches for gamertags using the OpenXBL unofficial Xbox Live API
- Checks title history to detect Minecraft Java Edition ownership
- Displays last online dates when available
- Implements rate limiting to avoid API blocks
- Saves results to JSON file
- Handles errors gracefully (non-existent gamertags, API errors)
- Interactive setup script for easy configuration

## Quick Start (Recommended)

### 1. Install Dependencies
```bash
pip install requests
```

### 2. Run the Interactive Setup
```bash
python setup_and_run.py
```

This will:
- Guide you through getting an OpenXBL API key
- Test your API key
- Let you choose the range of gamertags to check
- Run the lookup automatically

## Manual Setup (Alternative)

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Get OpenXBL API Key

1. Go to [https://xbl.io/](https://xbl.io/)
2. Click "Login with Xbox Live"
3. Sign in with your Microsoft/Xbox account
4. Navigate to your profile/dashboard to get your API key
5. Copy the API key

### 3. Test Your API Key
```bash
python test_api.py
```

### 4. Configure the Main Script

Edit `xbox_gamertag_lookup.py` and replace:
```python
API_KEY = "YOUR_OPENXBL_API_KEY_HERE"
```

With your actual API key:
```python
API_KEY = "your_actual_api_key_here"
```

## Usage Options

### Option 1: Interactive Setup (Recommended)
```bash
python setup_and_run.py
```

### Option 2: Manual Configuration
After setting up your API key in `xbox_gamertag_lookup.py`:

#### Test Run (First 100 gamertags)
```bash
python xbox_gamertag_lookup.py
```

#### Full Run (All 10,000 gamertags)
Edit the script and change:
```python
START_NUM = 0
END_NUM = 99  # Start with first 100 for testing
```

To:
```python
START_NUM = 0
END_NUM = 9999  # Full range
```

Then run:
```bash
python xbox_gamertag_lookup.py
```

**Note:** The full run will take several hours due to rate limiting (1 request per second).

## Output

The tool will:

1. Display progress in the console as it checks each gamertag
2. Show which gamertags own Minecraft Java Edition
3. Save results to `minecraft_java_owners.json`

Example output:
```
Checking Blue0000... Not found
Checking Blue0001... No Minecraft Java Edition  
Checking Blue0002... ✓ HAS MINECRAFT JAVA EDITION
...

FINAL RESULTS - GAMERTAGS WITH MINECRAFT JAVA EDITION
================================================================================
Found 3 gamertags with Minecraft Java Edition:

Gamertag: Blue0002
  Last seen: 2023-12-15T10:30:00Z
  XUID: ****************

Gamertag: Blue0157
  Last seen: 2023-11-28T15:45:00Z
  XUID: ****************
```

## Important Notes

### Rate Limiting
- The tool implements 1-second delays between API calls
- Full scan of 10,000 gamertags will take ~3+ hours
- This prevents being blocked by the API

### Minecraft Detection Limitations
- The tool detects Minecraft ownership through Xbox Live title history
- It may not perfectly distinguish between Java Edition and Bedrock Edition
- Microsoft's account migration has linked Java Edition to Xbox Live, but detection may not be 100% accurate

### API Limitations
- OpenXBL is an unofficial API with usage limits
- Some gamertags may not be found even if they exist
- API may occasionally return errors

## Troubleshooting

### "API Error" messages
- Check your API key is correct
- Verify your internet connection
- The API may be temporarily unavailable

### No results found
- Ensure gamertags in the Blue0000-Blue9999 range actually exist
- Minecraft Java Edition detection relies on Xbox Live integration

### Rate limiting issues
- Increase the `min_request_interval` in the script if you get blocked
- Consider running smaller batches

## Legal Notice

This tool uses the unofficial OpenXBL API. Please respect Xbox Live Terms of Service and use responsibly.
